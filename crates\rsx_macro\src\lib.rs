use proc_macro::TokenStream;

mod analysis;
mod codegen;
mod errors;
mod parsing;
mod validation;

use analysis::{GenerationContext, OptimizationLevel, RsxAnalyzer};
use codegen::RsxCodeGenerator;
use errors::RsxMacroError;
use parsing::RsxElement;
use validation::{RsxValidate, RsxValidator};

/// The main RSX macro for creating virtual DOM elements with enhanced validation and analysis
#[proc_macro]
pub fn rsx(input: TokenStream) -> TokenStream {
    // Enhanced parsing with better error handling
    let rsx_element = match syn::parse::<RsxElement>(input) {
        Ok(element) => element,
        Err(parse_error) => {
            // Convert syn::Error to our enhanced RsxMacroError with suggestions
            let enhanced_error = RsxMacroError::syntax_error(
                parse_error.span(),
                format!("Failed to parse RSX syntax: {}", parse_error),
            );
            return enhanced_error.to_syn_error().to_compile_error().into();
        }
    };

    // Enhanced validation with actionable error messages
    #[cfg(debug_assertions)]
    if let Err(validation_error) = rsx_element.validate() {
        return validation_error.to_syn_error().to_compile_error().into();
    }

    // Analyze element for optimization opportunities
    let analysis = RsxAnalyzer::analyze_element(&rsx_element);
    let tree_analysis = RsxAnalyzer::analyze_tree(&rsx_element);

    // Determine optimization level based on complexity
    let optimization_level = match analysis.complexity {
        analysis::ComplexityLevel::Simple => OptimizationLevel::Aggressive,
        analysis::ComplexityLevel::Moderate => OptimizationLevel::Release,
        analysis::ComplexityLevel::Dynamic => OptimizationLevel::Debug,
    };

    // Generate optimized code with analysis-driven context
    let context = GenerationContext::new(&rsx_element, optimization_level);

    // Apply optimization hints in debug mode
    #[cfg(debug_assertions)]
    {
        let hints = context.get_optimization_hints();
        if !hints.is_empty() {
            eprintln!(
                "RSX Optimization Hints for {}:",
                match &analysis.element_type {
                    analysis::ElementType::Component(name) => name.clone(),
                    analysis::ElementType::Text(content) =>
                        format!("Text({})", content.chars().take(20).collect::<String>()),
                    analysis::ElementType::Expression => "Expression".to_string(),
                }
            );
            for hint in hints {
                eprintln!("  - {}", hint);
            }
        }

        // Print complexity analysis
        if tree_analysis.complexity_score > 50.0 {
            // eprintln!(
            //     "RSX Complexity Warning: Score {:.1} (consider simplifying)",
            //     tree_analysis.complexity_score
            // );
        }
    }

    let output = RsxCodeGenerator::generate_with_context(&rsx_element, &context);
    output.into()
}

/// Enhanced RSX macro with explicit optimization level
/// Usage: rsx_optimized!({ <Text content="Hello" /> })
#[proc_macro]
pub fn rsx_optimized(input: TokenStream) -> TokenStream {
    // Enhanced parsing with better error handling
    let rsx_element = match syn::parse::<RsxElement>(input) {
        Ok(element) => element,
        Err(parse_error) => {
            let enhanced_error = RsxMacroError::syntax_error(
                parse_error.span(),
                format!("Failed to parse optimized RSX syntax: {}", parse_error),
            );
            return enhanced_error.to_syn_error().to_compile_error().into();
        }
    };

    // Always validate for optimized builds
    if let Err(validation_error) = rsx_element.validate() {
        return validation_error.to_syn_error().to_compile_error().into();
    }

    // Analyze element for comprehensive optimization
    let analysis = RsxAnalyzer::analyze_element(&rsx_element);
    let tree_analysis = RsxAnalyzer::analyze_tree(&rsx_element);

    // Use aggressive optimization for explicitly optimized builds
    let context = GenerationContext::new(&rsx_element, OptimizationLevel::Aggressive);
    let output = RsxCodeGenerator::generate_with_context(&rsx_element, &context);

    // Add detailed optimization analysis in debug mode
    #[cfg(debug_assertions)]
    {
        eprintln!("RSX Optimized Build Analysis:");
        eprintln!("  Element Type: {:?}", analysis.element_type);
        eprintln!("  Complexity: {:?}", analysis.complexity);
        eprintln!(
            "  Props Analysis: {} total, {} literal, {} expression",
            analysis.prop_analysis.total_props,
            analysis.prop_analysis.literal_props,
            analysis.prop_analysis.expression_props
        );
        eprintln!(
            "  Children Analysis: {} total, {} text, {} elements",
            analysis.children_analysis.total_children,
            analysis.children_analysis.text_children,
            analysis.children_analysis.element_children
        );
        eprintln!(
            "  Tree Analysis: {} elements, depth {}, {:.1} complexity score",
            tree_analysis.total_elements, tree_analysis.max_depth, tree_analysis.complexity_score
        );

        let hints = analysis.optimization_hints;
        if !hints.is_empty() {
            eprintln!("  Optimization Hints:");
            for hint in &hints {
                eprintln!("    - {}", hint);
            }
        }

        if !tree_analysis.optimization_opportunities.is_empty() {
            eprintln!("  Tree Optimization Opportunities:");
            for opportunity in &tree_analysis.optimization_opportunities {
                eprintln!("    - {}", opportunity);
            }
        }
    }

    output.into()
}

/// Debug-friendly RSX macro with extra validation and analysis
/// Usage: rsx_debug!({ <Text content="Hello" /> })
#[proc_macro]
pub fn rsx_debug(input: TokenStream) -> TokenStream {
    // Enhanced parsing with better error handling
    let rsx_element = match syn::parse::<RsxElement>(input) {
        Ok(element) => element,
        Err(parse_error) => {
            let enhanced_error = RsxMacroError::syntax_error(
                parse_error.span(),
                format!("Failed to parse debug RSX syntax: {}", parse_error),
            );
            return enhanced_error.to_syn_error().to_compile_error().into();
        }
    };

    // Always validate in debug mode with comprehensive checks
    if let Err(validation_error) = RsxValidator::validate_element(&rsx_element) {
        return validation_error.to_syn_error().to_compile_error().into();
    }

    // Comprehensive analysis for debug builds
    let analysis = RsxAnalyzer::analyze_element(&rsx_element);
    let tree_analysis = RsxAnalyzer::analyze_tree(&rsx_element);

    // Print comprehensive analysis information
    eprintln!("RSX Debug Analysis:");
    eprintln!("  Element Type: {:?}", analysis.element_type);
    eprintln!("  Complexity: {:?}", analysis.complexity);
    eprintln!("  Has Dynamic Content: {}", analysis.has_dynamic_content);
    eprintln!("  Is Self Closing: {}", analysis.is_self_closing);
    eprintln!("  Nesting Depth: {}", analysis.nesting_depth);

    // Detailed props analysis
    eprintln!("  Props Analysis:");
    eprintln!("    Total Props: {}", analysis.prop_analysis.total_props);
    eprintln!(
        "    Literal Props: {}",
        analysis.prop_analysis.literal_props
    );
    eprintln!(
        "    Expression Props: {}",
        analysis.prop_analysis.expression_props
    );
    eprintln!(
        "    Has Event Handlers: {}",
        analysis.prop_analysis.has_event_handlers
    );
    eprintln!(
        "    Has Style Props: {}",
        analysis.prop_analysis.has_style_props
    );
    if !analysis.prop_analysis.prop_names.is_empty() {
        eprintln!("    Prop Names: {:?}", analysis.prop_analysis.prop_names);
    }

    // Detailed children analysis
    eprintln!("  Children Analysis:");
    eprintln!(
        "    Total Children: {}",
        analysis.children_analysis.total_children
    );
    eprintln!(
        "    Text Children: {}",
        analysis.children_analysis.text_children
    );
    eprintln!(
        "    Element Children: {}",
        analysis.children_analysis.element_children
    );
    eprintln!(
        "    Expression Children: {}",
        analysis.children_analysis.expression_children
    );
    eprintln!(
        "    Max Nesting Depth: {}",
        analysis.children_analysis.max_nesting_depth
    );

    // Detailed tree analysis
    eprintln!("  Tree Analysis:");
    eprintln!("    Total Elements: {}", tree_analysis.total_elements);
    eprintln!("    Max Depth: {}", tree_analysis.max_depth);
    eprintln!("    Dynamic Elements: {}", tree_analysis.dynamic_elements);
    eprintln!(
        "    Complexity Score: {:.2}",
        tree_analysis.complexity_score
    );

    if !analysis.optimization_hints.is_empty() {
        eprintln!("  Optimization Hints:");
        for hint in &analysis.optimization_hints {
            eprintln!("    - {}", hint);
        }
    }

    if !tree_analysis.optimization_opportunities.is_empty() {
        eprintln!("  Tree Optimization Opportunities:");
        for opportunity in &tree_analysis.optimization_opportunities {
            eprintln!("    - {}", opportunity);
        }
    }

    // Generate debug-friendly code with comprehensive context
    let _context = GenerationContext::new(&rsx_element, OptimizationLevel::Debug);
    let output = codegen::CodeGenUtils::generate_debug(&rsx_element);
    output.into()
}
