use crate::hooks::event::set_current_component_id;
use crate::hooks::{HookContext, clear_hook_context, set_hook_context};
use std::cell::RefCell;
use std::collections::HashMap;
use std::rc::Rc;

// Thread-local registry to track component contexts by ID for testing
thread_local! {
    static COMPONENT_CONTEXTS: RefCell<HashMap<&'static str, Rc<HookContext>>> =
        RefCell::new(HashMap::new());
}

/// Professional component context manager for testing with component ID lifecycle
///
/// This function provides a realistic testing environment that:
/// - Maintains separate hook contexts per component ID
/// - When called with the same ID, it represents the next render of that component
/// - Automatically resets hook counter for re-renders (same ID)
/// - Creates new context only for new component IDs
/// - Properly cleans up contexts when test completes
/// - Follows real component lifecycle patterns
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_component_id;
///
/// with_component_id("MyComponent", |context| {
///     // First render - context is fresh
///     let memo1 = use_memo(|| expensive_calc(), Some(deps));
///
///     // Simulate re-render by calling again with same ID
///     with_component_id("MyComponent", |context| {
///         // Same component, next render - context persists, counter resets
///         let memo2 = use_memo(|| expensive_calc(), Some(deps));
///         // memo2 should return cached value if deps unchanged
///     });
/// });
/// ```
pub fn with_component_id<F, R>(component_id: &'static str, test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> R,
{
    let context = COMPONENT_CONTEXTS.with(|contexts| {
        let mut contexts = contexts.borrow_mut();

        if let Some(existing_context) = contexts.get(component_id) {
            // Same component ID - this is a re-render, reset hook counter
            existing_context.reset();
            existing_context.clone()
        } else {
            // New component ID - create fresh context
            let new_context = Rc::new(HookContext::new());
            contexts.insert(component_id, new_context.clone());
            new_context
        }
    });

    set_hook_context(context.clone());
    set_current_component_id(component_id.to_string());
    let result = test_fn(&context);
    clear_hook_context();

    result
}

/// Async version of component context manager for async hook testing
///
/// This function provides the same component lifecycle simulation as `with_component_id`
/// but supports async test functions for testing async hooks like `use_future`.
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_async_component_id;
///
/// with_async_component_id("MyAsyncComponent", async |context| {
///     // Async hook testing
///     let future = use_future(|| async { computation().await });
///     // Test assertions...
/// }).await;
/// ```
pub async fn with_async_component_id<F, Fut, R>(component_id: &'static str, test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> Fut,
    Fut: std::future::Future<Output = R>,
{
    let context = COMPONENT_CONTEXTS.with(|contexts| {
        let mut contexts = contexts.borrow_mut();

        if let Some(existing_context) = contexts.get(component_id) {
            // Same component ID - this is a re-render, reset hook counter
            existing_context.reset();
            existing_context.clone()
        } else {
            // New component ID - create fresh context
            let new_context = Rc::new(HookContext::new());
            contexts.insert(component_id, new_context.clone());
            new_context
        }
    });

    set_hook_context(context.clone());
    set_current_component_id(component_id.to_string());
    let result = test_fn(&context).await;
    clear_hook_context();

    result
}

/// Cleanup function to clear all component contexts (call at end of test suite)
pub fn cleanup_component_contexts() {
    COMPONENT_CONTEXTS.with(|contexts| {
        contexts.borrow_mut().clear();
    });
}

/// Professional test isolation wrapper that automatically handles cleanup
///
/// This function provides complete test isolation by:
/// - Ensuring clean state at the start of each test
/// - Automatically cleaning up component contexts after test completion
/// - Providing proper error handling and cleanup even if test panics
/// - Following professional testing patterns for resource management
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_test_isolate;
///
/// fn my_test() {
///     with_test_isolate(|| {
///         // Test code here - no need to manually call cleanup
///         with_component_id("MyComponent", |_| {
///             let memo = use_memo(|| computation(), Some(deps));
///             // Test assertions...
///         });
///
///         with_component_id("MyComponent", |_| {
///             // Next render - state persists automatically
///         });
///     });
///     // cleanup_component_contexts() called automatically
/// }
/// ```
pub fn with_test_isolate<F, R>(test_fn: F) -> R
where
    F: FnOnce() -> R,
{
    // Ensure clean state at start of test
    cleanup_component_contexts();

    // Use a guard to ensure cleanup happens even if test panics
    struct CleanupGuard;
    impl Drop for CleanupGuard {
        fn drop(&mut self) {
            cleanup_component_contexts();
        }
    }

    let _guard = CleanupGuard;

    // Run the test
    test_fn()

    // Cleanup happens automatically when _guard is dropped
}

/// Async version of test isolation wrapper for tokio tests
///
/// This function provides complete test isolation for async tests by:
/// - Ensuring clean state at the start of each test
/// - Automatically cleaning up component contexts after test completion
/// - Providing proper error handling and cleanup even if test panics
/// - Supporting async test functions with tokio::test
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_async_test_isolate;
///
/// #[tokio::test]
/// async fn my_async_test() {
///     with_async_test_isolate(async {
///         // Async test code here - no need to manually call cleanup
///         with_component_id("MyComponent", |_| {
///             let future = use_future(|| async { computation().await });
///             // Test assertions...
///         });
///     }).await;
///     // cleanup_component_contexts() called automatically
/// }
/// ```
pub async fn with_async_test_isolate<F, Fut, R>(test_fn: F) -> R
where
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = R>,
{
    // Ensure clean state at start of test
    cleanup_component_contexts();

    // Use a guard to ensure cleanup happens even if test panics
    struct AsyncCleanupGuard;
    impl Drop for AsyncCleanupGuard {
        fn drop(&mut self) {
            cleanup_component_contexts();
        }
    }

    let _guard = AsyncCleanupGuard;

    // Run the async test
    test_fn().await

    // Cleanup happens automatically when _guard is dropped
}

/// Simple test context helper for basic hook testing
///
/// This is a simpler alternative to `with_component_id` for tests that don't need
/// component lifecycle simulation. Creates a fresh context for each call.
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_hook_context;
///
/// fn simple_test() {
///     with_hook_context(|context| {
///         let (state, set_state) = use_state(42);
///         assert_eq!(state.get(), 42);
///     });
/// }
/// ```
pub fn with_hook_context<F, R>(test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> R,
{
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());
    set_current_component_id("test_component".to_string());

    let result = test_fn(&context);

    clear_hook_context();
    result
}

/// Async version of simple hook context helper for async hook testing
///
/// This is an async alternative to `with_hook_context` for tests that don't need
/// component lifecycle simulation but do need to test async hooks.
///
/// # Usage
/// ```rust,no_run
/// use terminus_ui_core::hooks::test_utils::with_async_hook_context;
///
/// with_async_hook_context(async |context| {
///     let future = use_future(|| async { computation().await });
///     // Test assertions...
/// }).await;
/// ```
pub async fn with_async_hook_context<F, Fut, R>(test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> Fut,
    Fut: std::future::Future<Output = R>,
{
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = test_fn(&context).await;

    clear_hook_context();
    result
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::hooks::use_state;

    #[test]
    fn test_with_test_isolate_example() {
        with_test_isolate(|| {
            // Test code with automatic cleanup
            with_component_id("ExampleComponent", |_| {
                let (state, set_state) = use_state(42);
                assert_eq!(state.get(), 42);
                set_state.set(100);
                assert_eq!(state.get(), 100);
            });

            // Next render - state persists
            with_component_id("ExampleComponent", |_| {
                let (state, _) = use_state(0); // Initial value ignored
                assert_eq!(state.get(), 100); // State from previous render
            });
        });
        // Cleanup happens automatically
    }

    #[test]
    fn test_with_hook_context_example() {
        with_hook_context(|_| {
            let (state, set_state) = use_state("test");
            assert_eq!(state.get(), "test");
            set_state.set("updated");
            assert_eq!(state.get(), "updated");
        });
    }

    #[test]
    fn test_component_isolation() {
        with_test_isolate(|| {
            // Component A
            with_component_id("ComponentA", |_| {
                let (state, set_state) = use_state(1);
                set_state.set(10);
                assert_eq!(state.get(), 10);
            });

            // Component B - completely isolated
            with_component_id("ComponentB", |_| {
                let (state, set_state) = use_state(2);
                set_state.set(20);
                assert_eq!(state.get(), 20);
            });

            // Component A again - state persists
            with_component_id("ComponentA", |_| {
                let (state, _) = use_state(999); // Ignored
                assert_eq!(state.get(), 10); // Previous state
            });
        });
    }

    #[tokio::test]
    async fn test_async_test_isolate_example() {
        with_async_test_isolate(|| async {
            // Test async utilities
            with_async_component_id("AsyncExampleComponent", |_| async {
                let (state, set_state) = use_state(42);
                assert_eq!(state.get(), 42);
                set_state.set(100);
                assert_eq!(state.get(), 100);
            })
            .await;

            // Next render - state persists
            with_async_component_id("AsyncExampleComponent", |_| async {
                let (state, _) = use_state(0); // Initial value ignored
                assert_eq!(state.get(), 100); // State from previous render
            })
            .await;
        })
        .await;
        // Cleanup happens automatically
    }

    #[tokio::test]
    async fn test_async_hook_context_example() {
        with_async_hook_context(|_| async {
            let (state, set_state) = use_state("async_test");
            assert_eq!(state.get(), "async_test");
            set_state.set("async_updated");
            assert_eq!(state.get(), "async_updated");
        })
        .await;
    }
}
