use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Comprehensive demonstration of the ScrollArea component
/// This example shows various scrolling patterns and configurations

#[derive(Props, Debug, Clone)]
pub struct ScrollDemoProps {
    pub title: String,
}

/// Main demo component showcasing different ScrollArea configurations
#[component(ScrollDemo)]
fn scroll_demo(props: ScrollDemoProps) -> Element {
    let (selected_demo, set_selected_demo) = use_state(0usize);
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());

    // Handle demo navigation
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Tab => {
                    let next_demo = (selected_demo.get() + 1) % 4;
                    set_selected_demo.call(next_demo);
                }
                KeyCode::BackTab => {
                    let prev_demo = if selected_demo.get() == 0 {
                        3
                    } else {
                        selected_demo.get() - 1
                    };
                    set_selected_demo.call(prev_demo);
                }
                _ => {}
            }
        }
    }

    // Scroll callback for demo 4
    let on_scroll = {
        let set_info = set_scroll_callback_info.clone();
        move |pos: (usize, usize)| {
            set_info.call(format!("Scroll position: Y={}, X={}", pos.0, pos.1));
        }
    };

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content={format!("Demo {}/4 - Use Tab/Shift+Tab to navigate, Q/Esc to quit", selected_demo.get() + 1)} />
            </Block>

            // Main content area
            {match selected_demo.get() {
                0 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // Vertical scrolling demo
                        <ScrollArea
                            title={Some("Vertical Scrolling".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Green))}
                            scroll_step={1}
                        >
                            <Text content="Line 1: This is a long scrollable content area" />
                            <Text content="Line 2: Use arrow keys or j/k to scroll vertically" />
                            <Text content="Line 3: The ScrollArea component manages overflow" />
                            <Text content="Line 4: Content that exceeds the viewport height" />
                            <Text content="Line 5: Will be scrollable with smooth navigation" />
                            <Text content="Line 6: PageUp/PageDown for faster scrolling" />
                            <Text content="Line 7: Following Shadcn UI design patterns" />
                            <Text content="Line 8: Professional TUI aesthetics" />
                            <Text content="Line 9: Type-safe component props" />
                            <Text content="Line 10: React-like component architecture" />
                            <Text content="Line 11: Scrollbars provide visual feedback" />
                            <Text content="Line 12: Vertical scrollbar shows position" />
                            <Text content="Line 13: Horizontal scrollbar for wide content" />
                            <Text content="Line 14: Smooth scrolling with keyboard controls" />
                            <Text content="Line 15: Bounded scrolling prevents overflow" />
                            <Text content="Line 16: Content height calculation is dynamic" />
                            <Text content="Line 17: Viewport height adapts to container" />
                            <Text content="Line 18: Scroll step controls movement speed" />
                            <Text content="Line 19: Multiple scroll orientations supported" />
                            <Text content="Line 20: Both vertical and horizontal modes" />
                            <Text content="Line 21: Customizable scrollbar symbols" />
                            <Text content="Line 22: Track and thumb visual indicators" />
                            <Text content="Line 23: Begin and end symbol customization" />
                            <Text content="Line 24: Style properties for theming" />
                            <Text content="Line 25: Border and title support" />
                            <Text content="Line 26: Integration with layout system" />
                            <Text content="Line 27: Event handling for scroll callbacks" />
                            <Text content="Line 28: State management with hooks" />
                            <Text content="Line 29: Performance optimized rendering" />
                            <Text content="Line 30: Memory efficient content handling" />
                            <Text content="Line 31: Cross-platform terminal support" />
                            <Text content="Line 32: Unicode character compatibility" />
                            <Text content="Line 33: Color and styling flexibility" />
                            <Text content="Line 34: Responsive design principles" />
                            <Text content="Line 35: Accessibility considerations" />
                            <Text content="Line 36: Developer-friendly API design" />
                            <Text content="Line 37: Comprehensive error handling" />
                            <Text content="Line 38: Documentation and examples" />
                            <Text content="Line 39: Testing and quality assurance" />
                            <Text content="Line 40: Community feedback integration" />
                            <Text content="Line 41: Continuous improvement process" />
                            <Text content="Line 42: Modern Rust development practices" />
                            <Text content="Line 43: Type safety and memory safety" />
                            <Text content="Line 44: Zero-cost abstractions where possible" />
                            <Text content="Line 45: Efficient terminal I/O operations" />
                            <Text content="Line 46: Minimal resource consumption" />
                            <Text content="Line 47: Fast startup and response times" />
                            <Text content="Line 48: Scalable architecture design" />
                            <Text content="Line 49: Modular component system" />
                            <Text content="Line 50: End of scrollable content area" />
                        </ScrollArea>

                        // Horizontal scrolling demo
                        <ScrollArea
                            title={Some("Horizontal Scrolling".to_string())}
                            orientation={ScrollOrientation::Horizontal}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Magenta))}
                            scroll_step={2}
                        >
                            {generate_wide_text_elements("Horizontal Content", 10)}
                        </ScrollArea>
                    </Layout>
                },
                1 => rsx! {
                    <ScrollArea
                        title={Some("Both Directions Scrolling".to_string())}
                        orientation={ScrollOrientation::Both}
                        show_scrollbars={true}
                        borders={Some(Borders::ALL)}
                        border_style={Some(Style::default().fg(Color::Yellow))}
                        scroll_step={1}
                    >
                        {generate_large_text_elements("Both Directions", 50, 148)}
                    </ScrollArea>
                },
                2 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // With scrollbar
                        <ScrollArea
                            title={Some("With Scrollbar".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Blue))}
                            scroll_step={2}
                        >
                            {generate_long_text_elements("Scrollbar Visible", 50)}
                        </ScrollArea>

                        // Without scrollbar
                        <ScrollArea
                            title={Some("Without Scrollbar".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={false}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Red))}
                            scroll_step={1}
                        >
                            {generate_long_text_elements("No Scrollbar", 50)}
                        </ScrollArea>
                    </Layout>
                },
                3 => rsx! {
                    <Layout direction={Direction::Vertical} constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0),
                    ]}>
                        // Callback info
                        <Block
                            title={"Scroll Callback Demo".to_string()}
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::White)}
                        >
                            <Text content={scroll_callback_info.get()} />
                        </Block>

                        // Scrollable area with callback
                        <ScrollArea
                            title={Some("Scroll to see callback in action".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Cyan))}
                            scroll_step={1}
                            on_scroll={on_scroll}
                        >
                            {generate_long_text_elements("Callback Content", 40)}
                        </ScrollArea>
                    </Layout>
                },
                _ => rsx! {
                    <Text content="Invalid demo selection" />
                },
            }}

            // Footer with instructions
            <Block
                title={"Controls".to_string()}
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓/←/→ = Scroll, Page Up/Down = Fast scroll, Home/End = Jump, Tab = Next demo, Q/Esc = Quit" />
            </Block>
        </Layout>
    }
}

/// Generate long text elements for vertical scrolling demos
fn generate_long_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!(
                    "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    prefix, i
                )} />
            }
        })
        .collect()
}

/// Generate wide text elements for horizontal scrolling demos
fn generate_wide_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!("{} line {} - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns.", prefix, i)} />
            }
        })
        .collect()
}

/// Generate large text elements for both-directions scrolling
fn generate_large_text_elements(prefix: &str, lines: usize, line_length: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            let base_text = format!("{} line {} - ", prefix, i);
            let padding = "Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ";
            let repeated_padding = padding.repeat((line_length * 10) / padding.len() + 1);
            let content = format!("{}{}", base_text, &repeated_padding[..line_length.min(repeated_padding.len())]);

            rsx! {
                <Text content={content} />
            }
        })
        .collect()
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = ScrollDemoProps {
        title: "🔄 ScrollArea Component Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ScrollDemo title={demo_props.title} />
    };

    render(element)
}
