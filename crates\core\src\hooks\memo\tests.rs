use super::*;
use crate::hooks::{HookContext, clear_hook_context, set_hook_context};
use std::rc::Rc;
use std::sync::atomic::{AtomicUsize, Ordering};

/// Helper function to set up a hook context for testing with component lifecycle management
///
/// This function provides a professional testing environment that:
/// - Creates a fresh hook context for each test
/// - Provides access to the context for manual resets (simulating re-renders)
/// - Automatically cleans up the context after the test
/// - Follows the same patterns as real component lifecycle
fn with_component_context<F, R>(test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> R,
{
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = test_fn(&context);

    clear_hook_context();
    result
}

/// Legacy helper function - kept for backward compatibility but marked as unused
#[allow(dead_code)]
fn with_test_context<F, R>(f: F) -> R
where
    F: FnOnce() -> R,
{
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = f();

    clear_hook_context();
    result
}

#[test]
fn test_memo_container_basic_operations() {
    let container = MemoContainer::<i32>::new();

    // Initially empty
    assert!(!container.is_initialized());
    assert_eq!(container.get(), None);
    assert_eq!(container.version(), 0);

    // Set a value
    container.set(42, None);
    assert!(container.is_initialized());
    assert_eq!(container.get(), Some(42));
    assert_eq!(container.version(), 1);

    // Update value
    container.set(84, None);
    assert_eq!(container.get(), Some(84));
    assert_eq!(container.version(), 2);
}

#[test]
fn test_memo_container_deps_changed() {
    let container = MemoContainer::<i32>::new();

    // No previous deps, current deps None - no change
    assert!(!container.deps_changed(None));

    // No previous deps, current deps Some - changed
    assert!(container.deps_changed(Some(&42)));

    // Set some deps
    container.set(100, Some(Box::new(42)));

    // Same deps - no change
    assert!(!container.deps_changed(Some(&42)));

    // Different deps - changed
    assert!(container.deps_changed(Some(&84)));

    // Had deps, now None - changed
    assert!(container.deps_changed(None));
}

#[test]
fn test_memo_handle_basic_operations() {
    let container = Arc::new(MemoContainer::new());
    container.set(42, None);

    let handle = MemoHandle::new(container);

    assert_eq!(handle.get(), Some(42));
    assert!(handle.is_computed());
    assert_eq!(handle.version(), 1);
}

#[test]
fn test_memo_handle_utility_methods() {
    #[derive(Clone, Debug, PartialEq)]
    struct TestData {
        value: i32,
        name: String,
    }

    let container = Arc::new(MemoContainer::new());
    let test_data = TestData {
        value: 42,
        name: "test".to_string(),
    };
    container.set(test_data.clone(), None);

    let handle = MemoHandle::new(container);

    // Test field access
    assert_eq!(handle.field(|data| data.value), Some(42));
    assert_eq!(
        handle.field(|data| data.name.clone()),
        Some("test".to_string())
    );

    // Test map
    assert_eq!(handle.map(|data| data.value * 2), Some(84));

    // Test unwrap_or_else
    assert_eq!(
        handle.unwrap_or_else(|| TestData {
            value: 0,
            name: "default".to_string()
        }),
        test_data
    );

    // Test with empty handle
    let empty_container = Arc::new(MemoContainer::new());
    let empty_handle = MemoHandle::new(empty_container);

    assert_eq!(empty_handle.field(|data: &TestData| data.value), None);
    assert_eq!(empty_handle.map(|data: TestData| data.value * 2), None);

    let default_data = TestData {
        value: 0,
        name: "default".to_string(),
    };
    assert_eq!(
        empty_handle.unwrap_or_else(|| default_data.clone()),
        default_data
    );
}

#[test]
fn test_use_memo_basic_functionality() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));
        let count_clone = computation_count.clone();

        // First call - should compute
        let memo1 = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            Some(10i32),
        );

        assert_eq!(memo1.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset hook context for next render (same context, just reset counter)
        context.reset();

        let count_clone2 = computation_count.clone();

        // Second call with same deps - should not recompute
        let memo2 = use_memo::<i32, _, i32>(
            move || {
                count_clone2.fetch_add(1, Ordering::SeqCst);
                84
            },
            Some(10i32),
        );

        assert_eq!(memo2.get(), Some(42)); // Should return cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // Should not increment
    });
}

#[test]
fn test_use_memo_dependency_changes() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // First computation with dep = 10
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            Some(10i32),
        );

        assert_eq!(memo.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Second computation with different dep = 20
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                84
            },
            Some(20i32),
        );

        assert_eq!(memo.get(), Some(84)); // Should compute new value
        assert_eq!(computation_count.load(Ordering::SeqCst), 2); // Should increment
    });
}

#[test]
fn test_use_memo_no_dependencies() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // First call with no deps - should compute
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, ()>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            None::<()>,
        );

        assert_eq!(memo.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Second call with no deps - should not recompute (already initialized)
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, ()>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                84
            },
            None::<()>,
        );

        assert_eq!(memo.get(), Some(42)); // Should return cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // Should not increment
    });
}

#[test]
fn test_use_memo_complex_dependencies() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // Test with tuple dependencies
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result1".to_string()
            },
            Some((10, "test".to_string())),
        );

        assert_eq!(memo.get(), Some("result1".to_string()));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Same dependencies - should not recompute
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result2".to_string()
            },
            Some((10, "test".to_string())),
        );

        assert_eq!(memo.get(), Some("result1".to_string())); // Cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // No increment

        // Reset for next render
        context.reset();

        // Different dependencies - should recompute
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result3".to_string()
            },
            Some((20, "test".to_string())), // Changed first element
        );

        assert_eq!(memo.get(), Some("result3".to_string())); // New value
        assert_eq!(computation_count.load(Ordering::SeqCst), 2); // Should increment
    });
}

#[test]
fn test_memo_handle_clone() {
    let container = Arc::new(MemoContainer::new());
    container.set(42, None);

    let handle1 = MemoHandle::new(container);
    let handle2 = handle1.clone();

    assert_eq!(handle1.get(), handle2.get());
    assert_eq!(handle1.version(), handle2.version());
    assert_eq!(handle1.is_computed(), handle2.is_computed());
}
