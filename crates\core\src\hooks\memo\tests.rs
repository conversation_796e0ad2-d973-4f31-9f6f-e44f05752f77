use super::*;
use crate::hooks::{HookContext, clear_hook_context, set_hook_context};
use std::rc::Rc;
use std::sync::atomic::{AtomicUsize, Ordering};

use std::cell::RefCell;
use std::collections::HashMap;

// Thread-local registry to track component contexts by ID for testing
thread_local! {
    static COMPONENT_CONTEXTS: RefCell<HashMap<&'static str, Rc<HookContext>>> =
        RefCell::new(HashMap::new());
}

/// Professional component context manager for testing with component ID lifecycle
///
/// This function provides a realistic testing environment that:
/// - Maintains separate hook contexts per component ID
/// - When called with the same ID, it represents the next render of that component
/// - Automatically resets hook counter for re-renders (same ID)
/// - Creates new context only for new component IDs
/// - Properly cleans up contexts when test completes
/// - Follows real component lifecycle patterns
///
/// # Usage
/// ```rust
/// with_component_id("MyComponent", |context| {
///     // First render - context is fresh
///     let memo1 = use_memo(|| expensive_calc(), Some(deps));
///
///     // Simulate re-render by calling again with same ID
///     with_component_id("MyComponent", |context| {
///         // Same component, next render - context persists, counter resets
///         let memo2 = use_memo(|| expensive_calc(), Some(deps));
///         // memo2 should return cached value if deps unchanged
///     });
/// });
/// ```
fn with_component_id<F, R>(component_id: &'static str, test_fn: F) -> R
where
    F: FnOnce(&Rc<HookContext>) -> R,
{
    let context = COMPONENT_CONTEXTS.with(|contexts| {
        let mut contexts = contexts.borrow_mut();

        if let Some(existing_context) = contexts.get(component_id) {
            // Same component ID - this is a re-render, reset hook counter
            existing_context.reset();
            existing_context.clone()
        } else {
            // New component ID - create fresh context
            let new_context = Rc::new(HookContext::new());
            contexts.insert(component_id, new_context.clone());
            new_context
        }
    });

    set_hook_context(context.clone());
    let result = test_fn(&context);
    clear_hook_context();

    result
}

/// Cleanup function to clear all component contexts (call at end of test suite)
#[allow(dead_code)]
fn cleanup_component_contexts() {
    COMPONENT_CONTEXTS.with(|contexts| {
        contexts.borrow_mut().clear();
    });
}

#[test]
fn test_memo_container_basic_operations() {
    let container = MemoContainer::<i32>::new();

    // Initially empty
    assert!(!container.is_initialized());
    assert_eq!(container.get(), None);
    assert_eq!(container.version(), 0);

    // Set a value
    container.set(42, None);
    assert!(container.is_initialized());
    assert_eq!(container.get(), Some(42));
    assert_eq!(container.version(), 1);

    // Update value
    container.set(84, None);
    assert_eq!(container.get(), Some(84));
    assert_eq!(container.version(), 2);
}

#[test]
fn test_memo_container_deps_changed() {
    let container = MemoContainer::<i32>::new();

    // No previous deps, current deps None - no change
    assert!(!container.deps_changed(None));

    // No previous deps, current deps Some - changed
    assert!(container.deps_changed(Some(&42)));

    // Set some deps
    container.set(100, Some(Box::new(42)));

    // Same deps - no change
    assert!(!container.deps_changed(Some(&42)));

    // Different deps - changed
    assert!(container.deps_changed(Some(&84)));

    // Had deps, now None - changed
    assert!(container.deps_changed(None));
}

#[test]
fn test_memo_handle_basic_operations() {
    let container = Arc::new(MemoContainer::new());
    container.set(42, None);

    let handle = MemoHandle::new(container);

    assert_eq!(handle.get(), Some(42));
    assert!(handle.is_computed());
    assert_eq!(handle.version(), 1);
}

#[test]
fn test_memo_handle_utility_methods() {
    #[derive(Clone, Debug, PartialEq)]
    struct TestData {
        value: i32,
        name: String,
    }

    let container = Arc::new(MemoContainer::new());
    let test_data = TestData {
        value: 42,
        name: "test".to_string(),
    };
    container.set(test_data.clone(), None);

    let handle = MemoHandle::new(container);

    // Test field access
    assert_eq!(handle.field(|data| data.value), Some(42));
    assert_eq!(
        handle.field(|data| data.name.clone()),
        Some("test".to_string())
    );

    // Test map
    assert_eq!(handle.map(|data| data.value * 2), Some(84));

    // Test unwrap_or_else
    assert_eq!(
        handle.unwrap_or_else(|| TestData {
            value: 0,
            name: "default".to_string()
        }),
        test_data
    );

    // Test with empty handle
    let empty_container = Arc::new(MemoContainer::new());
    let empty_handle = MemoHandle::new(empty_container);

    assert_eq!(empty_handle.field(|data: &TestData| data.value), None);
    assert_eq!(empty_handle.map(|data: TestData| data.value * 2), None);

    let default_data = TestData {
        value: 0,
        name: "default".to_string(),
    };
    assert_eq!(
        empty_handle.unwrap_or_else(|| default_data.clone()),
        default_data
    );
}

#[test]
fn test_use_memo_basic_functionality() {
    let computation_count = Rc::new(AtomicUsize::new(0));

    // First render of component
    with_component_id("TestComponent", |_context| {
        let count_clone = computation_count.clone();

        // First call - should compute
        let memo1 = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            Some(10i32),
        );

        assert_eq!(memo1.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);
    });

    // Second render of same component (same ID) - should use cached value
    with_component_id("TestComponent", |_context| {
        let count_clone2 = computation_count.clone();

        // Second call with same deps - should not recompute
        let memo2 = use_memo::<i32, _, i32>(
            move || {
                count_clone2.fetch_add(1, Ordering::SeqCst);
                84
            },
            Some(10i32),
        );

        assert_eq!(memo2.get(), Some(42)); // Should return cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // Should not increment
    });

    // Cleanup for this test
    cleanup_component_contexts();
}

#[test]
fn test_use_memo_dependency_changes() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // First computation with dep = 10
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            Some(10i32),
        );

        assert_eq!(memo.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Second computation with different dep = 20
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                84
            },
            Some(20i32),
        );

        assert_eq!(memo.get(), Some(84)); // Should compute new value
        assert_eq!(computation_count.load(Ordering::SeqCst), 2); // Should increment
    });
}

#[test]
fn test_use_memo_no_dependencies() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // First call with no deps - should compute
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, ()>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                42
            },
            None::<()>,
        );

        assert_eq!(memo.get(), Some(42));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Second call with no deps - should not recompute (already initialized)
        let count_clone = computation_count.clone();
        let memo = use_memo::<i32, _, ()>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                84
            },
            None::<()>,
        );

        assert_eq!(memo.get(), Some(42)); // Should return cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // Should not increment
    });
}

#[test]
fn test_use_memo_complex_dependencies() {
    with_component_context(|context| {
        let computation_count = Rc::new(AtomicUsize::new(0));

        // Test with tuple dependencies
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result1".to_string()
            },
            Some((10, "test".to_string())),
        );

        assert_eq!(memo.get(), Some("result1".to_string()));
        assert_eq!(computation_count.load(Ordering::SeqCst), 1);

        // Reset for next render
        context.reset();

        // Same dependencies - should not recompute
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result2".to_string()
            },
            Some((10, "test".to_string())),
        );

        assert_eq!(memo.get(), Some("result1".to_string())); // Cached value
        assert_eq!(computation_count.load(Ordering::SeqCst), 1); // No increment

        // Reset for next render
        context.reset();

        // Different dependencies - should recompute
        let count_clone = computation_count.clone();
        let memo = use_memo::<String, _, (i32, String)>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                "result3".to_string()
            },
            Some((20, "test".to_string())), // Changed first element
        );

        assert_eq!(memo.get(), Some("result3".to_string())); // New value
        assert_eq!(computation_count.load(Ordering::SeqCst), 2); // Should increment
    });
}

#[test]
fn test_memo_handle_clone() {
    let container = Arc::new(MemoContainer::new());
    container.set(42, None);

    let handle1 = MemoHandle::new(container);
    let handle2 = handle1.clone();

    assert_eq!(handle1.get(), handle2.get());
    assert_eq!(handle1.version(), handle2.version());
    assert_eq!(handle1.is_computed(), handle2.is_computed());
}

#[test]
fn test_component_id_isolation() {
    let computation_count_a = Rc::new(AtomicUsize::new(0));
    let computation_count_b = Rc::new(AtomicUsize::new(0));

    // Component A - first render
    with_component_id("ComponentA", |_context| {
        let count_clone = computation_count_a.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                100
            },
            Some(1),
        );
        assert_eq!(memo.get(), Some(100));
        assert_eq!(computation_count_a.load(Ordering::SeqCst), 1);
    });

    // Component B - first render (different component, should have separate context)
    with_component_id("ComponentB", |_context| {
        let count_clone = computation_count_b.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                200
            },
            Some(1), // Same deps as Component A, but different component
        );
        assert_eq!(memo.get(), Some(200)); // Different value
        assert_eq!(computation_count_b.load(Ordering::SeqCst), 1);
    });

    // Component A - second render (should use cached value)
    with_component_id("ComponentA", |_context| {
        let count_clone = computation_count_a.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                999 // This shouldn't be computed
            },
            Some(1), // Same deps
        );
        assert_eq!(memo.get(), Some(100)); // Should return cached value from first render
        assert_eq!(computation_count_a.load(Ordering::SeqCst), 1); // Should not increment
    });

    // Component B - second render (should use cached value)
    with_component_id("ComponentB", |_context| {
        let count_clone = computation_count_b.clone();
        let memo = use_memo::<i32, _, i32>(
            move || {
                count_clone.fetch_add(1, Ordering::SeqCst);
                888 // This shouldn't be computed
            },
            Some(1), // Same deps
        );
        assert_eq!(memo.get(), Some(200)); // Should return cached value from first render
        assert_eq!(computation_count_b.load(Ordering::SeqCst), 1); // Should not increment
    });

    // Cleanup for this test
    cleanup_component_contexts();
}
