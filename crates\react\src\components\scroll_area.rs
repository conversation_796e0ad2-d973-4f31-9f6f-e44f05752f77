//! ScrollArea and ScrollBar components for Terminus UI
//!
//! This module provides scrollable container components that follow Shadcn UI patterns
//! with professional TUI aesthetics and smooth scrolling behavior.

use crossterm::event::{Event, KeyCode};
use ratatui::Frame;
use ratatui::prelude::Rect;
use ratatui::{
    layout::{Constraint, Direction},
    style::Style,
    widgets::Borders,
};
use terminus_ui_component_macro::component;
use terminus_ui_core::PropRequirements;
use terminus_ui_core::{
    Callback, Children, ComponentProps, Element, FunctionalComponent, HasChildren,
    PropFieldMetadata, TrySetChildren, VirtualNode, use_event, use_state,
};
use terminus_ui_core::{LayoutProps, WidgetType};
use terminus_ui_props_macro::Props;

/// Scroll orientation for ScrollArea and ScrollBar components
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollOrientation {
    /// Vertical scrolling (up/down)
    Vertical,
    /// Horizontal scrolling (left/right)
    Horizontal,
    /// Both vertical and horizontal scrolling
    Both,
}

impl Default for ScrollOrientation {
    fn default() -> Self {
        Self::Vertical
    }
}

/// Position of the scrollbar relative to the content area
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollbarPosition {
    /// Top edge (for horizontal scrollbars)
    Top,
    /// Bottom edge (for horizontal scrollbars)
    Bottom,
    /// Left edge (for vertical scrollbars)
    Left,
    /// Right edge (for vertical scrollbars)
    Right,
}

impl Default for ScrollbarPosition {
    fn default() -> Self {
        Self::Right
    }
}

/// Props for the ScrollBar component
///
/// This component renders a customizable scrollbar with various visual options
/// following the Shadcn UI design patterns.
#[derive(Props, Debug, Clone)]
pub struct ScrollBarProps {
    /// Orientation of the scrollbar
    pub orientation: ScrollOrientation,
    /// Position of the scrollbar
    pub position: ScrollbarPosition,
    /// Current scroll position (0.0 to 1.0)
    pub scroll_position: f64,
    /// Visible content ratio (0.0 to 1.0) - determines thumb size
    pub content_ratio: f64,
    /// Custom begin symbol (e.g., "↑" for vertical, "←" for horizontal)
    pub begin_symbol: Option<String>,
    /// Custom end symbol (e.g., "↓" for vertical, "→" for horizontal)
    pub end_symbol: Option<String>,
    /// Custom thumb symbol for the scrollbar handle
    pub thumb_symbol: Option<String>,
    /// Custom track symbol for the scrollbar track
    pub track_symbol: Option<String>,
    /// Style for the scrollbar
    pub style: Option<Style>,
    /// Whether the scrollbar is visible
    pub visible: bool,
}

/// Props for the ScrollArea component
///
/// This is the main scrollable container component that manages content overflow
/// and provides smooth scrolling behavior with optional scrollbars.
#[derive(Props, Debug, Clone)]
pub struct ScrollAreaProps {
    /// Scroll orientation support
    pub orientation: ScrollOrientation,
    /// Whether to show scrollbars
    pub show_scrollbars: bool,
    /// Position of vertical scrollbar
    pub vertical_scrollbar_position: ScrollbarPosition,
    /// Position of horizontal scrollbar  
    pub horizontal_scrollbar_position: ScrollbarPosition,
    /// Custom scrollbar style
    pub scrollbar_style: Option<Style>,
    /// Scroll step size for keyboard navigation
    pub scroll_step: usize,
    /// Whether content should wrap
    pub wrap: bool,
    /// Callback for scroll events
    pub on_scroll: Option<Callback<(usize, usize)>>, // (vertical_offset, horizontal_offset)
    /// Custom begin symbols for scrollbars
    pub scrollbar_begin_symbols: Option<(String, String)>, // (vertical, horizontal)
    /// Custom end symbols for scrollbars
    pub scrollbar_end_symbols: Option<(String, String)>, // (vertical, horizontal)
    /// Block properties for the container
    pub title: Option<String>,
    pub borders: Option<Borders>,
    pub border_style: Option<Style>,
    /// Child components to render inside the scroll area
    #[children]
    pub children: Children,
}

/// ScrollBar component implementation
///
/// Renders a customizable scrollbar with proper thumb positioning and visual indicators.
/// Follows Shadcn UI design patterns with professional TUI aesthetics.
#[component(ScrollBar)]
pub fn scroll_bar(props: ScrollBarProps) -> Element {
    use terminus_ui_core::{TextProps, WidgetType};

    if !props.visible || props.content_ratio >= 1.0 {
        return VirtualNode::widget(
            WidgetType::Text,
            TextProps {
                content: "".to_string(),
                style: None,
            },
            vec![],
        );
    }

    // For now, render a simple placeholder that shows scrollbar info
    let info = format!(
        "[ScrollBar {}:{:.1}%]",
        match props.orientation {
            ScrollOrientation::Vertical => "V",
            ScrollOrientation::Horizontal => "H",
            ScrollOrientation::Both => "B",
        },
        props.scroll_position * 100.0
    );

    VirtualNode::widget(
        WidgetType::Text,
        TextProps {
            content: info,
            style: props.style,
        },
        vec![],
    )
}

/// ScrollArea component implementation
///
/// Main scrollable container component that manages content overflow and provides
/// smooth scrolling behavior with optional scrollbars following Shadcn UI patterns.
#[component(ScrollArea)]
pub fn scroll_area(props: ScrollAreaProps, area: Rect) -> Element {
    // Initialize scroll state using hooks
    let (vertical_scroll, set_vertical_scroll) = use_state(0usize);
    let (horizontal_scroll, set_horizontal_scroll) = use_state(0usize);

    // Use scroll_step prop directly, default to 1 if not provided or is 0
    let step_size = if props.scroll_step == 0 {
        1
    } else {
        props.scroll_step
    };

    // Flatten children early so we can use the correct count for all scroll calculations
    let children_vec: Vec<VirtualNode> = flatten_children(&props.children);
    let children_len = children_vec.len();

    // Calculate max content width early (before children_vec is moved)
    let max_content_width = children_vec
        .iter()
        .filter_map(|child| {
            if let VirtualNode::Widget {
                props: child_props, ..
            } = child
            {
                child_props
                    .get::<TextProps>()
                    .map(|text_props| text_props.content.len())
            } else {
                None
            }
        })
        .max()
        .unwrap_or(0);

    // Handle keyboard events for scrolling
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Up | KeyCode::Char('k') => {
                    if props.orientation == ScrollOrientation::Vertical
                        || props.orientation == ScrollOrientation::Both
                    {
                        let new_scroll = vertical_scroll.get().saturating_sub(step_size);
                        set_vertical_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, horizontal_scroll.get()));
                        }
                    }
                }
                KeyCode::Down | KeyCode::Char('j') => {
                    if props.orientation == ScrollOrientation::Vertical
                        || props.orientation == ScrollOrientation::Both
                    {
                        // Calculate max vertical scroll based on content height and viewport
                        let content_height = children_len;
                        let viewport_height = (area.height as usize).saturating_sub(4);
                        let max_scroll = content_height.saturating_sub(viewport_height);

                        let new_scroll = (vertical_scroll.get() + step_size).min(max_scroll);
                        set_vertical_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, horizontal_scroll.get()));
                        }
                    }
                }
                KeyCode::Left | KeyCode::Char('h') => {
                    if props.orientation == ScrollOrientation::Horizontal
                        || props.orientation == ScrollOrientation::Both
                    {
                        let new_scroll = horizontal_scroll.get().saturating_sub(step_size);
                        set_horizontal_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((vertical_scroll.get(), new_scroll));
                        }
                    }
                }
                KeyCode::Right | KeyCode::Char('l') => {
                    if props.orientation == ScrollOrientation::Horizontal
                        || props.orientation == ScrollOrientation::Both
                    {
                        // Use pre-calculated max content width

                        // Calculate viewport width dynamically from the actual area
                        // Subtract space for borders (2 chars) and vertical scrollbar (1 char)
                        let viewport_width = (area.width as usize).saturating_sub(3);
                        let max_scroll = max_content_width.saturating_sub(viewport_width);

                        let new_scroll = (horizontal_scroll.get() + step_size).min(max_scroll);
                        set_horizontal_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((vertical_scroll.get(), new_scroll));
                        }
                    }
                }
                KeyCode::PageUp => {
                    if props.orientation == ScrollOrientation::Vertical
                        || props.orientation == ScrollOrientation::Both
                    {
                        // Calculate max vertical scroll based on content height and viewport
                        let content_height = children_len;
                        let viewport_height = (area.height as usize).saturating_sub(4);
                        let max_scroll = content_height.saturating_sub(viewport_height);

                        let new_scroll = vertical_scroll
                            .get()
                            .saturating_sub(step_size * 10)
                            .min(max_scroll);
                        set_vertical_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, horizontal_scroll.get()));
                        }
                    }
                }
                KeyCode::PageDown => {
                    if props.orientation == ScrollOrientation::Vertical
                        || props.orientation == ScrollOrientation::Both
                    {
                        // Calculate max vertical scroll based on content height and viewport
                        let content_height = children_len;
                        let viewport_height = (area.height as usize).saturating_sub(4);
                        let max_scroll = content_height.saturating_sub(viewport_height);

                        let new_scroll = (vertical_scroll.get() + step_size * 10).min(max_scroll);
                        set_vertical_scroll.call(new_scroll);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, horizontal_scroll.get()));
                        }
                    }
                }
                _ => {}
            }
        }
    }

    // Note: Removed unused block variable creation since we're using raw VirtualNode

    // Build the scrollable content area using raw VirtualNode
    use ratatui::widgets::ScrollbarOrientation;
    use terminus_ui_core::{BlockProps, LayoutProps, ScrollbarProps, TextProps, WidgetType};

    // Create scrollbars if enabled
    let mut layout_children = vec![];

    // Create inner content layout to display all children as separate lines
    // (children_vec and children_len are already defined at the top of the function)

    // Apply vertical scrolling by skipping lines based on scroll position
    let vertical_offset = vertical_scroll.get();
    let horizontal_offset = horizontal_scroll.get();

    // Calculate viewport height dynamically from the actual area
    // Subtract space for borders (2 lines) and scrollbar/debug info (2 lines)
    let viewport_height = (area.height as usize).saturating_sub(4);

    let mut visible_children: Vec<VirtualNode> = if props.orientation == ScrollOrientation::Vertical
        || props.orientation == ScrollOrientation::Both
    {
        children_vec
            .into_iter()
            .skip(vertical_offset)
            .take(viewport_height) // Limit to viewport height
            .collect()
    } else {
        children_vec
    };

    // Apply horizontal scrolling by modifying text content
    if (props.orientation == ScrollOrientation::Horizontal
        || props.orientation == ScrollOrientation::Both)
        && horizontal_offset > 0
    {
        visible_children = visible_children
            .into_iter()
            .map(|child| match child {
                VirtualNode::Widget {
                    widget_type: WidgetType::Text,
                    props: text_props,
                    children,
                } => {
                    if let Some(text_props) = text_props.get::<TextProps>() {
                        let content = &text_props.content;
                        let scrolled_content = if content.len() > horizontal_offset {
                            content.chars().skip(horizontal_offset).collect::<String>()
                        } else {
                            String::new()
                        };

                        VirtualNode::widget(
                            WidgetType::Text,
                            TextProps {
                                content: scrolled_content,
                                style: text_props.style,
                            },
                            children,
                        )
                    } else {
                        VirtualNode::Widget {
                            widget_type: WidgetType::Text,
                            props: text_props,
                            children,
                        }
                    }
                }
                _ => child,
            })
            .collect();
    }

    let visible_count = visible_children.len();
    let content_constraints = vec![Constraint::Length(1); visible_count];

    let content_layout = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Vertical),
            constraints: Some(content_constraints),
            margin: None,
        },
        visible_children,
    );

    // Add content to layout
    layout_children.push(content_layout);

    // Create the main content area first
    let main_content = layout_children
        .into_iter()
        .find(|child| {
            matches!(
                child,
                VirtualNode::Widget {
                    widget_type: WidgetType::Layout,
                    ..
                }
            )
        })
        .unwrap_or_else(|| {
            VirtualNode::widget(
                WidgetType::Text,
                TextProps {
                    content: "No content".to_string(),
                    style: None,
                },
                vec![],
            )
        });

    // Create horizontal layout for content + vertical scrollbar
    let mut horizontal_layout_children = vec![main_content];

    // Add vertical scrollbar to the right if enabled and needed
    let content_height = children_len; // Use flattened children count
    let viewport_height = (area.height as usize).saturating_sub(4); // Account for borders
    let needs_vertical_scrolling = content_height > viewport_height;

    if props.show_scrollbars
        && needs_vertical_scrolling
        && (props.orientation == ScrollOrientation::Vertical
            || props.orientation == ScrollOrientation::Both)
    {
        // Set up vertical scrollbar with proper ScrollbarState parameters
        // Ensure we have valid scrollbar parameters
        let content_height = content_height.max(1); // Minimum 1 to avoid division by zero
        let viewport_height = viewport_height.max(1); // Minimum 1 to avoid issues
        let scroll_position = vertical_scroll
            .get()
            .min(content_height.saturating_sub(viewport_height));

        let vertical_scrollbar = VirtualNode::widget(
            WidgetType::Scrollbar,
            ScrollbarProps {
                orientation: ScrollbarOrientation::VerticalRight,
                position: scroll_position as u16, // Ensure position is within valid bounds
                content_length: content_height as u16,
                viewport_length: viewport_height as u16, // Pass viewport length for proper thumb sizing
                style: props.scrollbar_style,
                begin_symbol: props
                    .scrollbar_begin_symbols
                    .as_ref()
                    .map(|(v, _)| v.clone()),
                end_symbol: props.scrollbar_end_symbols.as_ref().map(|(v, _)| v.clone()),
                thumb_symbol: Some("█".to_string()),
                track_symbol: Some("│".to_string()),
            },
            vec![],
        );
        horizontal_layout_children.push(vertical_scrollbar);
    }

    let content_with_vscroll = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Horizontal),
            constraints: Some(vec![
                Constraint::Min(0),    // Content area
                Constraint::Length(1), // Vertical scrollbar
            ]),
            margin: None,
        },
        horizontal_layout_children,
    );

    // Create vertical layout for content + horizontal scrollbar + debug info
    let mut vertical_layout_children = vec![content_with_vscroll];

    // Add horizontal scrollbar at the bottom if enabled and needed
    // (max_content_width was calculated earlier before children_vec was moved)

    // Set up horizontal scrollbar with proper ScrollbarState parameters
    let viewport_width = (area.width as usize).saturating_sub(3); // Account for borders and vertical scrollbar
    let needs_horizontal_scrolling = max_content_width > viewport_width;

    if props.show_scrollbars
        && needs_horizontal_scrolling
        && (props.orientation == ScrollOrientation::Horizontal
            || props.orientation == ScrollOrientation::Both)
    {
        // Ensure we have valid scrollbar parameters
        let max_content_width = max_content_width.max(1); // Minimum 1 to avoid division by zero
        let viewport_width = viewport_width.max(1); // Minimum 1 to avoid issues
        let scroll_position = horizontal_scroll
            .get()
            .min(max_content_width.saturating_sub(viewport_width));

        let horizontal_scrollbar = VirtualNode::widget(
            WidgetType::Scrollbar,
            ScrollbarProps {
                orientation: ScrollbarOrientation::HorizontalBottom,
                position: scroll_position as u16, // Ensure position is within valid bounds
                content_length: max_content_width as u16, // Total content width
                viewport_length: viewport_width as u16, // Pass viewport width for proper thumb sizing
                style: props.scrollbar_style,
                begin_symbol: props
                    .scrollbar_begin_symbols
                    .as_ref()
                    .map(|(_, h)| h.clone()),
                end_symbol: props.scrollbar_end_symbols.as_ref().map(|(_, h)| h.clone()),
                thumb_symbol: Some("█".to_string()),
                track_symbol: Some("─".to_string()),
            },
            vec![],
        );
        vertical_layout_children.push(horizontal_scrollbar);
    }

    // Add scroll indicator text for debugging
    // Debug scrollbar parameters
    let debug_content_height = children_len;
    let debug_viewport_height = (area.height as usize).saturating_sub(4);
    let debug_max_scroll = debug_content_height.saturating_sub(debug_viewport_height);

    let scroll_indicator = VirtualNode::widget(
        WidgetType::Text,
        TextProps {
            content: format!(
                "V:{}/{} H:{}/{} | CH:{} VH:{} | Ratio:{:.2}",
                vertical_scroll.get(),
                debug_max_scroll,
                horizontal_scroll.get(),
                0, // placeholder for horizontal max
                debug_content_height,
                debug_viewport_height,
                if debug_max_scroll > 0 {
                    vertical_scroll.get() as f32 / debug_max_scroll as f32
                } else {
                    0.0
                }
            ),
            style: None,
        },
        vec![],
    );
    vertical_layout_children.push(scroll_indicator);

    let layout = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Vertical),
            constraints: Some(vec![
                Constraint::Min(0),    // Content area with scrollbars
                Constraint::Length(1), // Horizontal scrollbar
                Constraint::Length(1), // Scroll indicator
            ]),
            margin: None,
        },
        vertical_layout_children,
    );

    // Create main container block
    VirtualNode::widget(
        WidgetType::Block,
        BlockProps {
            title: props.title.clone(),
            borders: props.borders,
            border_style: props.border_style,
        },
        vec![layout],
    )
}

/// Flatten children to handle Vec<Element> expressions that get wrapped in Layout
/// This ensures ScrollArea can properly count and process individual elements
fn flatten_children(children: &Children) -> Vec<VirtualNode> {
    let mut flattened = Vec::new();

    for child in children.iter() {
        match child {
            // If child is a Layout with vertical direction (created by Vec<Element>), flatten its children
            VirtualNode::Widget {
                widget_type,
                props,
                children: layout_children,
            } if *widget_type == WidgetType::Layout => {
                // Check if this is a vertical layout (likely from Vec<Element>)
                if let Some(layout_props) = props.get::<LayoutProps>() {
                    if layout_props.direction == Some(ratatui::layout::Direction::Vertical) {
                        // Flatten the layout children
                        flattened.extend(layout_children.iter().cloned());
                        continue;
                    }
                }
                // If not a vertical layout, keep as is
                flattened.push(child.clone());
            }
            // For all other children, keep as is
            _ => flattened.push(child.clone()),
        }
    }

    flattened
}
