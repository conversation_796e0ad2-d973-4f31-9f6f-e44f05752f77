use parking_lot::RwLock;
use std::sync::Arc;

use crate::hooks::{EffectDependencies, with_hook_context};

/// A thread-safe memoized value container that holds the computed value
/// This is the core storage for useMemo hook values
pub struct MemoContainer<T> {
    /// The current memoized value, protected by RwLock for efficient reads
    value: RwLock<Option<T>>,
    /// Previous dependencies for comparison
    prev_deps: RwLock<Option<Box<dyn EffectDependencies>>>,
    /// Version counter to track memo recomputations (useful for debugging and optimization)
    version: RwLock<u64>,
}

impl<T> MemoContainer<T> {
    /// Create a new memo container
    pub fn new() -> Self {
        Self {
            value: RwLock::new(None),
            prev_deps: RwLock::new(None),
            version: RwLock::new(0),
        }
    }

    /// Get the current memoized value (thread-safe read)
    pub fn get(&self) -> Option<T>
    where
        T: Clone,
    {
        self.value.read().clone()
    }

    /// Set a new memoized value and update dependencies (thread-safe write)
    pub fn set(&self, new_value: T, new_deps: Option<Box<dyn EffectDependencies>>) {
        {
            let mut value = self.value.write();
            *value = Some(new_value);
        }

        {
            let mut deps = self.prev_deps.write();
            *deps = new_deps;
        }

        // Increment version counter
        {
            let mut version = self.version.write();
            *version += 1;
        }

        // TODO: Trigger re-render notification if needed
        // This would integrate with the component re-render system
    }

    /// Check if dependencies have changed
    pub fn deps_changed(&self, current_deps: Option<&dyn EffectDependencies>) -> bool {
        let prev_deps = self.prev_deps.read();

        match (&*prev_deps, current_deps) {
            (None, None) => false,   // Both None - no change
            (None, Some(_)) => true, // Had no deps, now has deps - changed
            (Some(_), None) => true, // Had deps, now no deps - changed
            (Some(prev), Some(current)) => !current.deps_eq(prev.as_ref()), // Compare deps
        }
    }

    /// Get the current version (useful for change detection)
    pub fn version(&self) -> u64 {
        *self.version.read()
    }

    /// Check if the memo has been initialized
    pub fn is_initialized(&self) -> bool {
        self.value.read().is_some()
    }
}

impl<T> Default for MemoContainer<T> {
    fn default() -> Self {
        Self::new()
    }
}

impl<T> std::fmt::Debug for MemoContainer<T>
where
    T: std::fmt::Debug,
{
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("MemoContainer")
            .field("value", &self.value.read())
            .field("version", &self.version.read())
            .field("has_deps", &self.prev_deps.read().is_some())
            .finish()
    }
}

/// A handle to a memoized value that provides access to the computed result
/// This is what gets returned to the component from useMemo
pub struct MemoHandle<T> {
    /// Reference to the shared memo container
    container: Arc<MemoContainer<T>>,
}

impl<T> MemoHandle<T> {
    /// Create a new memo handle with a container
    pub fn new(container: Arc<MemoContainer<T>>) -> Self {
        Self { container }
    }

    /// Get the current memoized value
    pub fn get(&self) -> Option<T>
    where
        T: Clone,
    {
        self.container.get()
    }

    /// Get the current version of the memo (useful for change detection)
    pub fn version(&self) -> u64 {
        self.container.version()
    }

    /// Check if the memo has been computed
    pub fn is_computed(&self) -> bool {
        self.container.is_initialized()
    }

    /// Get a reference to the underlying container (for advanced use cases)
    pub fn container(&self) -> &Arc<MemoContainer<T>> {
        &self.container
    }
}

impl<T> Clone for MemoHandle<T> {
    fn clone(&self) -> Self {
        Self {
            container: self.container.clone(),
        }
    }
}

impl<T> std::fmt::Debug for MemoHandle<T>
where
    T: std::fmt::Debug,
{
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("MemoHandle")
            .field("container", &self.container)
            .finish()
    }
}

/// Additional utility methods for MemoHandle
impl<T> MemoHandle<T>
where
    T: Clone,
{
    /// Access a field of the memoized value using a getter function
    /// This is useful for accessing nested properties without cloning the entire value
    ///
    /// # Example
    ///
    /// ```rust
    /// #[derive(Clone)]
    /// struct ExpensiveData {
    ///     result: i32,
    ///     metadata: String,
    /// }
    ///
    /// let memo = use_memo(|| ExpensiveData {
    ///     result: expensive_computation(),
    ///     metadata: "computed".to_string(),
    /// }, Some(input_value));
    ///
    /// let result = memo.field(|data| data.result);
    /// ```
    pub fn field<F, R>(&self, getter: F) -> Option<R>
    where
        F: FnOnce(&T) -> R,
    {
        self.get().map(|value| getter(&value))
    }

    /// Map the memoized value to a different type
    /// This is useful for deriving computed values from memoized data
    ///
    /// # Example
    ///
    /// ```rust
    /// let expensive_memo = use_memo(|| expensive_computation(), Some(input));
    /// let is_positive = expensive_memo.map(|result| result > 0);
    /// ```
    pub fn map<F, R>(&self, mapper: F) -> Option<R>
    where
        F: FnOnce(T) -> R,
    {
        self.get().map(mapper)
    }

    /// Get the memoized value or compute a default if not available
    /// This is useful for providing fallback values
    ///
    /// # Example
    ///
    /// ```rust
    /// let memo = use_memo(|| expensive_computation(), Some(input));
    /// let value = memo.unwrap_or_else(|| default_value());
    /// ```
    pub fn unwrap_or_else<F>(&self, default: F) -> T
    where
        F: FnOnce() -> T,
    {
        self.get().unwrap_or_else(default)
    }
}

/// React-style useMemo hook that provides memoization for expensive computations
///
/// This function exactly mirrors React's useMemo behavior:
/// - Only recomputes the value when dependencies change (using equality comparison)
/// - Caches and returns the same value when dependencies haven't changed
/// - Handles initial computation on first call
/// - Supports any type T that can be cloned or moved
/// - Thread-safe for use in async contexts
///
/// # Examples
///
/// ## Basic Memoization
/// ```rust
/// use terminus_ui::prelude::*;
///
/// #[component(ExpensiveComponent)]
/// fn expensive_component() -> Element {
///     let (input, set_input) = use_state(10);
///
///     // Expensive computation only runs when input changes
///     let expensive_result = use_memo(|| {
///         // Simulate expensive computation
///         std::thread::sleep(std::time::Duration::from_millis(100));
///         input * input * input
///     }, Some(input));
///
///     let result = expensive_result.get().unwrap_or(0);
///
///     rsx! {
///         <Block>
///             <Text content={format!("Input: {}, Result: {}", input, result)} />
///             <Button
///                 text="Increment"
///                 on_click={move || set_input.update(|prev| prev + 1)}
///             />
///         </Block>
///     }
/// }
/// ```
///
/// ## Memoization with Multiple Dependencies
/// ```rust
/// use terminus_ui::prelude::*;
///
/// #[component(Calculator)]
/// fn calculator() -> Element {
///     let (a, set_a) = use_state(5);
///     let (b, set_b) = use_state(3);
///     let (operation, set_operation) = use_state("add");
///
///     // Memoize calculation based on multiple dependencies
///     let result = use_memo(|| {
///         match operation.as_str() {
///             "add" => a + b,
///             "multiply" => a * b,
///             "power" => a.pow(b as u32),
///             _ => 0,
///         }
///     }, Some((a, b, operation.clone())));
///
///     let calculated_value = result.get().unwrap_or(0);
///
///     rsx! {
///         <Block>
///             <Text content={format!("Result: {}", calculated_value)} />
///         </Block>
///     }
/// }
/// ```
///
/// ## Memoization with Complex Data Structures
/// ```rust
/// use terminus_ui::prelude::*;
///
/// #[derive(Clone, Debug)]
/// struct ProcessedData {
///     filtered: Vec<i32>,
///     sum: i32,
///     average: f64,
/// }
///
/// #[component(DataProcessor)]
/// fn data_processor() -> Element {
///     let (raw_data, set_raw_data) = use_state(vec![1, 2, 3, 4, 5]);
///     let (threshold, set_threshold) = use_state(3);
///
///     // Memoize expensive data processing
///     let processed = use_memo(|| {
///         let filtered: Vec<i32> = raw_data.iter()
///             .filter(|&&x| x >= threshold)
///             .copied()
///             .collect();
///
///         let sum: i32 = filtered.iter().sum();
///         let average = if filtered.is_empty() {
///             0.0
///         } else {
///             sum as f64 / filtered.len() as f64
///         };
///
///         ProcessedData { filtered, sum, average }
///     }, Some((raw_data.clone(), threshold)));
///
///     let data = processed.get();
///
///     rsx! {
///         <Block>
///             <Text content={format!("Processed: {:?}", data)} />
///         </Block>
///     }
/// }
/// ```
///
/// ## Memoization without Dependencies (Run Once)
/// ```rust
/// use terminus_ui::prelude::*;
///
/// #[component(InitializedComponent)]
/// fn initialized_component() -> Element {
///     // Compute expensive initial value only once
///     let initial_config = use_memo(|| {
///         // Expensive initialization that should only run once
///         load_configuration_from_file()
///     }, Some(())); // Empty tuple as dependency = run once
///
///     let config = initial_config.get();
///
///     rsx! {
///         <Text content={format!("Config loaded: {:?}", config.is_some())} />
///     }
/// }
/// ```
///
/// ## Advanced Usage with Field Access
/// ```rust
/// use terminus_ui::prelude::*;
///
/// #[derive(Clone)]
/// struct LargeDataSet {
///     items: Vec<String>,
///     metadata: HashMap<String, String>,
///     computed_hash: String,
/// }
///
/// #[component(DataViewer)]
/// fn data_viewer() -> Element {
///     let (filter, set_filter) = use_state("".to_string());
///
///     let processed_data = use_memo(|| {
///         // Expensive data processing
///         process_large_dataset(&filter)
///     }, Some(filter.clone()));
///
///     // Efficiently access specific fields without cloning entire dataset
///     let item_count = processed_data.field(|data| data.items.len()).unwrap_or(0);
///     let hash = processed_data.field(|data| data.computed_hash.clone()).unwrap_or_default();
///
///     rsx! {
///         <Block>
///             <Text content={format!("Items: {}, Hash: {}", item_count, hash)} />
///         </Block>
///     }
/// }
/// ```
///
/// # Performance Notes
///
/// - Memoized values are cached until dependencies change
/// - Dependency comparison uses EffectDependencies trait for efficient equality checking
/// - Thread-safe access using RwLock for concurrent reads
/// - Memory efficient with Arc-based sharing
/// - Version tracking enables efficient change detection
///
/// # Error Handling
///
/// This function will panic if called outside of a component render context.
/// Always ensure useMemo is called within a component function.
///
/// # Thread Safety
///
/// The returned MemoHandle is thread-safe and can be safely shared across
/// async tasks and threads.
pub fn use_memo<T, F, Deps>(factory: F, deps: impl Into<Option<Deps>>) -> MemoHandle<T>
where
    T: Clone + 'static,
    F: FnOnce() -> T + 'static,
    Deps: EffectDependencies + Clone + PartialEq + 'static,
{
    let deps = deps.into();

    with_hook_context(|ctx| {
        // Get or initialize the memo container for this hook
        let container_ref = ctx.get_or_init_state(|| Arc::new(MemoContainer::<T>::new()));

        // Extract the Arc<MemoContainer<T>> from Rc<RefCell<Arc<MemoContainer<T>>>>
        let container = container_ref.borrow().clone();

        // Check if we need to recompute
        let should_compute = match &deps {
            None => {
                // No dependencies - only compute if not initialized
                !container.is_initialized()
            }
            Some(current_deps) => {
                // Check if dependencies have changed or if not initialized
                !container.is_initialized() || container.deps_changed(Some(current_deps))
            }
        };

        if should_compute {
            // Compute the new value
            let new_value = factory();

            // Store the new value and dependencies
            let new_deps = deps.map(|d| d.clone_deps());
            container.set(new_value, new_deps);
        }

        // Return the memo handle
        MemoHandle::new(container)
    })
}

#[cfg(test)]
mod tests;
