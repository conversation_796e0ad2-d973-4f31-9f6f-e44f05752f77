//! Comprehensive tests for the use_event hook
//!
//! This module contains extensive tests covering:
//! - Basic event handling for all event types
//! - Event consumption and processing tracking
//! - Multiple component event handling
//! - Edge cases and error conditions
//! - Integration with other hooks
//! - Thread safety and concurrent access

use crate::{
    hooks::{
        event::{
            CURRENT_EVENT, get_current_event_context, mark_event_processed,
            set_current_event_context, use_event,
        },
        test_utils::{with_component_id, with_hook_context},
    },
    use_state,
};
use crossterm::event::{
    Event, KeyCode, KeyEvent, KeyModifiers, MouseButton, MouseEvent, MouseEventKind,
};
use std::sync::Arc;

// Helper function to reset the event state before and after each test
fn setup_test() {
    let mut event_state = CURRENT_EVENT.write().unwrap();
    event_state.event = None;
    event_state.processed_by.clear();
}

// Helper function to reset the processed map only
fn reset_processed_map() {
    let mut event_state = CURRENT_EVENT.write().unwrap();
    event_state.processed_by.clear();
}

// Helper function to run a test with proper setup and cleanup
fn with_test_isolation<F>(test_fn: F)
where
    F: FnOnce() + std::panic::UnwindSafe,
{
    // Setup before test
    setup_test();

    // Run the test and catch any panics
    let result = std::panic::catch_unwind(test_fn);

    // Always cleanup after test, even if it panicked
    setup_test();

    // Re-throw the panic if the test failed
    if let Err(panic) = result {
        std::panic::resume_unwind(panic);
    }
}

/// Test basic event retrieval with no event set
#[test]
fn test_use_event_no_event() {
    with_test_isolation(|| {
        with_hook_context(|_context| {
            // No event set - should return None
            let event = use_event();
            assert!(event.is_none());
        });
    });
}

/// Test basic keyboard event handling
#[test]
fn test_use_event_keyboard_basic() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('a'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('a'));
                assert_eq!(key.modifiers, KeyModifiers::NONE);
            } else {
                panic!("Expected keyboard event");
            }
        });
    });
}

/// Test keyboard event with modifiers
#[test]
fn test_use_event_keyboard_with_modifiers() {
    with_test_isolation(|| {
        // Set a keyboard event with Ctrl modifier
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('q'), KeyModifiers::CONTROL));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('q'));
                assert!(key.modifiers.contains(KeyModifiers::CONTROL));
            } else {
                panic!("Expected keyboard event with modifiers");
            }
        });
    });
}

/// Test special key codes
#[test]
fn test_use_event_special_keys() {
    with_test_isolation(|| {
        let test_keys = vec![
            KeyCode::Enter,
            KeyCode::Esc,
            KeyCode::Tab,
            KeyCode::Backspace,
            KeyCode::Delete,
            KeyCode::Up,
            KeyCode::Down,
            KeyCode::Left,
            KeyCode::Right,
            KeyCode::F(1),
            KeyCode::F(12),
        ];

        for key_code in test_keys {
            // Clear any previous event
            setup_test();

            let key_event = Event::Key(KeyEvent::new(key_code, KeyModifiers::NONE));
            set_current_event_context(Some(Arc::new(key_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Key(key)) = event {
                    assert_eq!(key.code, key_code);
                } else {
                    panic!("Expected keyboard event for key: {:?}", key_code);
                }
            });
        }
    });
}

/// Test mouse event handling
#[test]
fn test_use_event_mouse_basic() {
    with_test_isolation(|| {
        // Set a mouse click event
        let mouse_event = Event::Mouse(MouseEvent {
            kind: MouseEventKind::Down(MouseButton::Left),
            column: 10,
            row: 5,
            modifiers: KeyModifiers::NONE,
        });
        set_current_event_context(Some(Arc::new(mouse_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Mouse(mouse)) = event {
                assert_eq!(mouse.kind, MouseEventKind::Down(MouseButton::Left));
                assert_eq!(mouse.column, 10);
                assert_eq!(mouse.row, 5);
                assert_eq!(mouse.modifiers, KeyModifiers::NONE);
            } else {
                panic!("Expected mouse event");
            }
        });
    });
}

/// Test different mouse event types
#[test]
fn test_use_event_mouse_types() {
    with_test_isolation(|| {
        let mouse_events = vec![
            MouseEventKind::Down(MouseButton::Left),
            MouseEventKind::Down(MouseButton::Right),
            MouseEventKind::Down(MouseButton::Middle),
            MouseEventKind::Up(MouseButton::Left),
            MouseEventKind::Drag(MouseButton::Left),
            MouseEventKind::Moved,
            MouseEventKind::ScrollDown,
            MouseEventKind::ScrollUp,
        ];

        for (i, mouse_kind) in mouse_events.into_iter().enumerate() {
            // Clear any previous event
            set_current_event_context(None);

            let mouse_event = Event::Mouse(MouseEvent {
                kind: mouse_kind,
                column: i as u16,
                row: i as u16,
                modifiers: KeyModifiers::NONE,
            });
            set_current_event_context(Some(Arc::new(mouse_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Mouse(mouse)) = event {
                    assert_eq!(mouse.kind, mouse_kind);
                    assert_eq!(mouse.column, i as u16);
                    assert_eq!(mouse.row, i as u16);
                } else {
                    panic!("Expected mouse event for kind: {:?}", mouse_kind);
                }
            });
        }
    });
}

/// Test resize event handling
#[test]
fn test_use_event_resize() {
    with_test_isolation(|| {
        // Set a resize event
        let resize_event = Event::Resize(80, 24);
        set_current_event_context(Some(Arc::new(resize_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Resize(width, height)) = event {
                assert_eq!(width, 80);
                assert_eq!(height, 24);
            } else {
                panic!("Expected resize event");
            }
        });
    });
}

/// Test focus gained event
#[test]
fn test_use_event_focus_gained() {
    with_test_isolation(|| {
        // Set a focus gained event
        let focus_event = Event::FocusGained;
        set_current_event_context(Some(Arc::new(focus_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::FocusGained) = event {
                // Expected
            } else {
                panic!("Expected focus gained event");
            }
        });
    });
}

/// Test focus lost event
#[test]
fn test_use_event_focus_lost() {
    with_test_isolation(|| {
        // Set a focus lost event
        let focus_event = Event::FocusLost;
        set_current_event_context(Some(Arc::new(focus_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::FocusLost) = event {
                // Expected
            } else {
                panic!("Expected focus lost event");
            }
        });
    });
}

/// Test paste event
#[test]
fn test_use_event_paste() {
    with_test_isolation(|| {
        // Set a paste event
        let paste_content = "Hello, World!".to_string();
        let paste_event = Event::Paste(paste_content.clone());
        set_current_event_context(Some(Arc::new(paste_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Paste(content)) = event {
                assert_eq!(content, paste_content);
            } else {
                panic!("Expected paste event");
            }
        });
    });
}

/// Test event consumption - different hook calls get different hook indices
#[test]
fn test_use_event_multiple_hook_calls() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('a'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_hook_context(|_context| {
            // First call should get the event (hook index 0)
            let event1 = use_event();
            assert!(event1.is_some());

            // Second call should also get the event (hook index 1)
            let event2 = use_event();
            assert!(event2.is_some());

            // Both events should be the same
            if let (Some(Event::Key(key1)), Some(Event::Key(key2))) = (event1, event2) {
                assert_eq!(key1.code, KeyCode::Char('a'));
                assert_eq!(key2.code, KeyCode::Char('a'));
            } else {
                panic!("Expected both calls to return keyboard events");
            }
        });
    });
}

/// Test event consumption across different components
#[test]
fn test_use_event_multiple_components() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('x'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        // First component gets the event (hook index 0)
        with_component_id("Component1", |_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('x'));
            } else {
                panic!("Expected keyboard event in component 1");
            }
        });

        // Second component cannot access the same event because it was already consumed
        // This demonstrates the current event consumption behavior
        with_component_id("Component2", |_context| {
            let event = use_event();
            // The event should be None because it was consumed by Component1
            assert!(event.is_none());
        });
    });
}

/// Test component can't access same event twice in different renders
#[test]
fn test_use_event_component_multiple_renders() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('y'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        // First render
        with_component_id("TestComponent", |_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('y'));
            } else {
                panic!("Expected keyboard event in first render");
            }
        });

        // Second render of same component - should not get event again
        with_component_id("TestComponent", |_context| {
            let event = use_event();
            assert!(event.is_none());
        });
    });
}

/// Test event clearing and new event setting
#[test]
fn test_use_event_clear_and_reset() {
    with_test_isolation(|| {
        // Set initial event
        let key_event1 = Event::Key(KeyEvent::new(KeyCode::Char('1'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event1.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('1'));
            }
        });

        // Clear event and reset processed map
        setup_test();

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_none());
        });

        // Set new event
        let key_event2 = Event::Key(KeyEvent::new(KeyCode::Char('2'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event2.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Key(key)) = event {
                assert_eq!(key.code, KeyCode::Char('2'));
            }
        });
    });
}

/// Test integration with use_state hook
#[test]
fn test_use_event_with_use_state() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('k'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_component_id("StateIntegrationComponent", |_context| {
            let (key_count, set_key_count) = use_state(0);

            // Initial state
            assert_eq!(key_count.get(), 0);

            // Handle event and update state
            if let Some(Event::Key(_)) = use_event() {
                set_key_count.set(key_count.get() + 1);
            }

            // State should be updated
            assert_eq!(key_count.get(), 1);
        });
    });
}

/// Test multiple hooks in same component
#[test]
fn test_use_event_multiple_hooks_same_component() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_component_id("MultiHookComponent", |_context| {
            let (count, set_count) = use_state(0);

            // First use_event call should get the event (hook index after state hooks)
            let event1 = use_event();
            assert!(event1.is_some());

            // Second use_event call should also get the event (different hook index)
            let event2 = use_event();
            assert!(event2.is_some());

            // Update state based on first event
            if let Some(Event::Key(key)) = event1 {
                if key.code == KeyCode::Enter {
                    set_count.set(count.get() + 1);
                }
            }

            assert_eq!(count.get(), 1);
        });
    });
}

/// Test event processing with different modifier combinations
#[test]
fn test_use_event_modifier_combinations() {
    with_test_isolation(|| {
        let modifier_combinations = vec![
            KeyModifiers::NONE,
            KeyModifiers::CONTROL,
            KeyModifiers::ALT,
            KeyModifiers::SHIFT,
            KeyModifiers::CONTROL | KeyModifiers::ALT,
            KeyModifiers::CONTROL | KeyModifiers::SHIFT,
            KeyModifiers::ALT | KeyModifiers::SHIFT,
            KeyModifiers::CONTROL | KeyModifiers::ALT | KeyModifiers::SHIFT,
        ];

        for modifiers in modifier_combinations {
            // Clear any previous event and reset processed map
            setup_test();

            let key_event = Event::Key(KeyEvent::new(KeyCode::Char('m'), modifiers));
            set_current_event_context(Some(Arc::new(key_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Key(key)) = event {
                    assert_eq!(key.code, KeyCode::Char('m'));
                    assert_eq!(key.modifiers, modifiers);
                } else {
                    panic!("Expected keyboard event with modifiers: {:?}", modifiers);
                }
            });
        }
    });
}

/// Test mouse event with modifiers
#[test]
fn test_use_event_mouse_with_modifiers() {
    with_test_isolation(|| {
        // Set a mouse event with Ctrl modifier
        let mouse_event = Event::Mouse(MouseEvent {
            kind: MouseEventKind::Down(MouseButton::Left),
            column: 15,
            row: 10,
            modifiers: KeyModifiers::CONTROL,
        });
        set_current_event_context(Some(Arc::new(mouse_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Mouse(mouse)) = event {
                assert_eq!(mouse.kind, MouseEventKind::Down(MouseButton::Left));
                assert_eq!(mouse.column, 15);
                assert_eq!(mouse.row, 10);
                assert!(mouse.modifiers.contains(KeyModifiers::CONTROL));
            } else {
                panic!("Expected mouse event with modifiers");
            }
        });
    });
}

/// Test rapid event changes
#[test]
fn test_use_event_rapid_changes() {
    with_test_isolation(|| {
        for i in 0..100 {
            // Clear previous event and reset processed map
            setup_test();

            // Set a new event each iteration
            let expected_char = (b'a' + (i % 26) as u8) as char;
            let key_event = Event::Key(KeyEvent::new(
                KeyCode::Char(expected_char),
                KeyModifiers::NONE,
            ));
            set_current_event_context(Some(Arc::new(key_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Key(key)) = event {
                    assert_eq!(key.code, KeyCode::Char(expected_char));
                } else {
                    panic!("Expected keyboard event for iteration {}", i);
                }
            });
        }
    });
}

/// Test event with empty paste content
#[test]
fn test_use_event_empty_paste() {
    with_test_isolation(|| {
        // Set a paste event with empty content
        let paste_event = Event::Paste(String::new());
        set_current_event_context(Some(Arc::new(paste_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Paste(content)) = event {
                assert!(content.is_empty());
            } else {
                panic!("Expected paste event with empty content");
            }
        });
    });
}

/// Test event with large paste content
#[test]
fn test_use_event_large_paste() {
    with_test_isolation(|| {
        // Set a paste event with large content
        let large_content = "x".repeat(10000);
        let paste_event = Event::Paste(large_content.clone());
        set_current_event_context(Some(Arc::new(paste_event.clone())));

        with_hook_context(|_context| {
            let event = use_event();
            assert!(event.is_some());

            if let Some(Event::Paste(content)) = event {
                assert_eq!(content.len(), 10000);
                assert_eq!(content, large_content);
            } else {
                panic!("Expected paste event with large content");
            }
        });
    });
}

/// Test resize event with extreme values
#[test]
fn test_use_event_resize_extreme_values() {
    with_test_isolation(|| {
        let test_cases = vec![
            (0, 0),         // Minimum values
            (1, 1),         // Very small
            (65535, 65535), // Maximum u16 values
            (1920, 1080),   // Common screen resolution
            (80, 24),       // Traditional terminal size
        ];

        for (width, height) in test_cases {
            // Clear any previous event
            set_current_event_context(None);

            let resize_event = Event::Resize(width, height);
            set_current_event_context(Some(Arc::new(resize_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Resize(w, h)) = event {
                    assert_eq!(w, width);
                    assert_eq!(h, height);
                } else {
                    panic!("Expected resize event for {}x{}", width, height);
                }
            });
        }
    });
}

/// Test mouse event with extreme coordinates
#[test]
fn test_use_event_mouse_extreme_coordinates() {
    with_test_isolation(|| {
        let test_coordinates = vec![
            (0, 0),         // Origin
            (65535, 65535), // Maximum u16 values
            (1, 1),         // Near origin
            (1000, 500),    // Typical coordinates
        ];

        for (column, row) in test_coordinates {
            // Clear any previous event
            set_current_event_context(None);

            let mouse_event = Event::Mouse(MouseEvent {
                kind: MouseEventKind::Down(MouseButton::Left),
                column,
                row,
                modifiers: KeyModifiers::NONE,
            });
            set_current_event_context(Some(Arc::new(mouse_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Mouse(mouse)) = event {
                    assert_eq!(mouse.column, column);
                    assert_eq!(mouse.row, row);
                } else {
                    panic!("Expected mouse event for coordinates ({}, {})", column, row);
                }
            });
        }
    });
}

/// Test direct context functions
#[test]
fn test_event_context_functions_direct() {
    with_test_isolation(|| {
        // Test with no event set
        let event = get_current_event_context();
        assert!(event.is_none());

        // Set an event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('d'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_hook_context(|_context| {
            // Get event directly
            let event = get_current_event_context();
            assert!(event.is_some());

            if let Some(event_arc) = event {
                if let Event::Key(key) = &*event_arc {
                    assert_eq!(key.code, KeyCode::Char('d'));
                } else {
                    panic!("Expected keyboard event");
                }
            }
        });
    });
}

/// Test mark_event_processed function directly
#[test]
fn test_mark_event_processed_direct() {
    with_test_isolation(|| {
        // Set an event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('p'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        with_hook_context(|_context| {
            // Get event first time - should work
            let event1 = get_current_event_context();
            assert!(event1.is_some());

            // Manually mark as processed for hook index 0
            mark_event_processed(0);

            // Try to get event again with same hook index - should return None
            let event2 = get_current_event_context();
            assert!(event2.is_none());
        });
    });
}

/// Test event consumption behavior with many components
#[test]
fn test_use_event_many_components() {
    with_test_isolation(|| {
        // Set a keyboard event
        let key_event = Event::Key(KeyEvent::new(KeyCode::Char('z'), KeyModifiers::NONE));
        set_current_event_context(Some(Arc::new(key_event.clone())));

        // Test with multiple components - only the first one should get the event
        let mut event_received_count = 0;

        for i in 0..10 {
            // Use a static string pattern to avoid lifetime issues
            match i % 5 {
                0 => with_component_id("ComponentA", |_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_received_count += 1;
                        if let Some(Event::Key(key)) = event {
                            assert_eq!(key.code, KeyCode::Char('z'));
                        }
                    }
                }),
                1 => with_component_id("ComponentB", |_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_received_count += 1;
                        if let Some(Event::Key(key)) = event {
                            assert_eq!(key.code, KeyCode::Char('z'));
                        }
                    }
                }),
                2 => with_component_id("ComponentC", |_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_received_count += 1;
                        if let Some(Event::Key(key)) = event {
                            assert_eq!(key.code, KeyCode::Char('z'));
                        }
                    }
                }),
                3 => with_component_id("ComponentD", |_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_received_count += 1;
                        if let Some(Event::Key(key)) = event {
                            assert_eq!(key.code, KeyCode::Char('z'));
                        }
                    }
                }),
                _ => with_component_id("ComponentE", |_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_received_count += 1;
                        if let Some(Event::Key(key)) = event {
                            assert_eq!(key.code, KeyCode::Char('z'));
                        }
                    }
                }),
            }
        }

        // Only the first component should have received the event
        assert_eq!(event_received_count, 1);
    });
}

/// Test event state consistency across hook calls
#[test]
fn test_use_event_state_consistency() {
    with_test_isolation(|| {
        // Set a mouse event
        let mouse_event = Event::Mouse(MouseEvent {
            kind: MouseEventKind::Down(MouseButton::Right),
            column: 42,
            row: 24,
            modifiers: KeyModifiers::ALT,
        });
        set_current_event_context(Some(Arc::new(mouse_event.clone())));

        with_component_id("ConsistencyTestComponent", |_context| {
            // Multiple state hooks
            let (count1, set_count1) = use_state(0);
            let (count2, set_count2) = use_state(0);
            let (count3, set_count3) = use_state(0);

            // Event hook
            let event = use_event();
            assert!(event.is_some());

            // Process event and update all states
            if let Some(Event::Mouse(mouse)) = event {
                assert_eq!(mouse.kind, MouseEventKind::Down(MouseButton::Right));
                assert_eq!(mouse.column, 42);
                assert_eq!(mouse.row, 24);
                assert!(mouse.modifiers.contains(KeyModifiers::ALT));

                set_count1.set(count1.get() + 1);
                set_count2.set(count2.get() + 2);
                set_count3.set(count3.get() + 3);
            }

            // Verify state updates
            assert_eq!(count1.get(), 1);
            assert_eq!(count2.get(), 2);
            assert_eq!(count3.get(), 3);

            // Second event call should also get the event (different hook index)
            let event2 = use_event();
            assert!(event2.is_some());
        });
    });
}

/// Test event handling with Unicode characters
#[test]
fn test_use_event_unicode_characters() {
    with_test_isolation(|| {
        let unicode_chars = vec!['α', 'β', 'γ', '中', '文', '🚀', '🎉', '💻'];

        for unicode_char in unicode_chars {
            // Clear any previous event
            set_current_event_context(None);

            let key_event = Event::Key(KeyEvent::new(
                KeyCode::Char(unicode_char),
                KeyModifiers::NONE,
            ));
            set_current_event_context(Some(Arc::new(key_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());

                if let Some(Event::Key(key)) = event {
                    assert_eq!(key.code, KeyCode::Char(unicode_char));
                } else {
                    panic!("Expected keyboard event for Unicode char: {}", unicode_char);
                }
            });
        }
    });
}

/// Test event handling performance
#[test]
fn test_use_event_performance() {
    use std::time::Instant;

    with_test_isolation(|| {
        let start = Instant::now();

        // Perform many event operations
        for i in 0..1000 {
            // Clear previous event and reset processed map for each iteration
            setup_test();

            let key_event = Event::Key(KeyEvent::new(
                KeyCode::Char((b'a' + (i % 26) as u8) as char),
                KeyModifiers::NONE,
            ));
            set_current_event_context(Some(Arc::new(key_event.clone())));

            with_hook_context(|_context| {
                let event = use_event();
                assert!(event.is_some());
            });
        }

        let duration = start.elapsed();

        // Performance assertion - should complete quickly
        assert!(
            duration.as_millis() < 1000,
            "Event operations took too long: {:?}",
            duration
        );
    });
}

/// Test thread safety of event context (basic test)
#[test]
fn test_use_event_thread_safety_basic() {
    use std::sync::Barrier;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use std::thread;

    // Clean up before starting the test
    with_test_isolation(|| {
        let event_count = Arc::new(AtomicUsize::new(0));
        let barrier = Arc::new(Barrier::new(4));

        let mut handles = vec![];

        // Spawn multiple threads that handle events
        for i in 0..3 {
            let event_count_clone = event_count.clone();
            let barrier_clone = barrier.clone();

            let handle = thread::spawn(move || {
                // Each thread works with the global event context
                // Set a unique event for this thread
                let key_event = Event::Key(KeyEvent::new(
                    KeyCode::Char((b'a' + i as u8) as char),
                    KeyModifiers::NONE,
                ));
                set_current_event_context(Some(Arc::new(key_event.clone())));

                barrier_clone.wait();

                with_hook_context(|_context| {
                    let event = use_event();
                    if event.is_some() {
                        event_count_clone.fetch_add(1, Ordering::Relaxed);
                    }
                });
            });

            handles.push(handle);
        }

        // Wait for all threads to be ready
        barrier.wait();

        // Wait for all threads to complete
        for handle in handles {
            handle.join().unwrap();
        }

        // At least one thread should have processed an event
        // (Due to race conditions, not all threads may see the same event)
        assert!(event_count.load(Ordering::Relaxed) >= 1);
    });
}
